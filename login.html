<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التسجيل - AL-SALAMAT</title>
    <link rel="stylesheet" href="styles.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <style>
        /* Login Page Specific Styles */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .login-logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 2rem;
            letter-spacing: 1px;
        }

        .form-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(102, 126, 234, 0.1);
        }

        .tab-btn {
            flex: 1;
            padding: 1rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #667eea;
        }

        .tab-btn.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .form-container {
            position: relative;
        }

        .form-section {
            display: none;
        }

        .form-section.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .submit-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
        }

        .back-link {
            display: inline-block;
            margin-top: 2rem;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #764ba2;
        }

        .forgot-password {
            margin-top: 1rem;
            text-align: center;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        /* Message Styles */
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            text-align: center;
            font-weight: 500;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.1);
            color: #4CAF50;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .message.error {
            background: rgba(244, 67, 54, 0.1);
            color: #f44336;
            border: 1px solid rgba(244, 67, 54, 0.3);
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading .submit-btn {
            background: #ccc !important;
            cursor: not-allowed;
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .login-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .login-logo {
                font-size: 2rem;
            }

            .tab-btn {
                padding: 0.8rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-logo">AL-SALAMAT</div>
            
            <div class="form-tabs">
                <button class="tab-btn active" onclick="switchTab('login')">تسجيل الدخول</button>
                <button class="tab-btn" onclick="switchTab('register')">إنشاء حساب</button>
            </div>

            <div class="form-container">
                <!-- Message Display -->
                <div id="message-container"></div>
                
                <!-- Login Form -->
                <div id="login-form" class="form-section active">
                    <form onsubmit="handleLogin(event)" id="login-form-element">
                        <div class="form-group">
                            <label for="login-email">البريد الإلكتروني</label>
                            <input type="email" id="login-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="login-password">كلمة المرور</label>
                            <input type="password" id="login-password" name="password" required>
                        </div>
                        <button type="submit" class="submit-btn">تسجيل الدخول</button>
                        <div class="forgot-password">
                            <a href="#" onclick="alert('سيتم إضافة هذه الميزة قريباً')">نسيت كلمة المرور؟</a>
                        </div>
                    </form>
                </div>

                <!-- Register Form -->
                <div id="register-form" class="form-section">
                    <form onsubmit="handleRegister(event)" id="register-form-element">
                        <div class="form-group">
                            <label for="register-name">الاسم الكامل</label>
                            <input type="text" id="register-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="register-email">البريد الإلكتروني</label>
                            <input type="email" id="register-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="register-phone">رقم الهاتف</label>
                            <input type="tel" id="register-phone" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label for="register-password">كلمة المرور</label>
                            <input type="password" id="register-password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="register-confirm-password">تأكيد كلمة المرور</label>
                            <input type="password" id="register-confirm-password" name="confirm-password" required>
                        </div>
                        <button type="submit" class="submit-btn">إنشاء حساب</button>
                    </form>
                </div>
            </div>

            <a href="index.html" class="back-link">← العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const database = firebase.database();

        // Utility functions
        function showMessage(message, type = 'success') {
            const messageContainer = document.getElementById('message-container');
            messageContainer.innerHTML = `<div class="message ${type}">${message}</div>`;

            if (type === 'success') {
                setTimeout(() => {
                    messageContainer.innerHTML = '';
                }, 3000);
            }
        }

        function setLoading(formId, isLoading) {
            const form = document.getElementById(formId);
            if (isLoading) {
                form.classList.add('loading');
            } else {
                form.classList.remove('loading');
            }
        }

        function switchTab(tab) {
            document.getElementById('message-container').innerHTML = '';
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.form-section').forEach(form => form.classList.remove('active'));
            event.target.classList.add('active');
            document.getElementById(tab + '-form').classList.add('active');
        }

        function getErrorMessage(errorCode) {
            switch (errorCode) {
                case 'auth/email-already-in-use':
                    return 'هذا البريد الإلكتروني مستخدم بالفعل';
                case 'auth/weak-password':
                    return 'كلمة المرور ضعيفة جداً';
                case 'auth/invalid-email':
                    return 'البريد الإلكتروني غير صحيح';
                case 'auth/user-not-found':
                    return 'المستخدم غير موجود';
                case 'auth/wrong-password':
                    return 'كلمة المرور غير صحيحة';
                case 'auth/too-many-requests':
                    return 'تم تجاوز عدد المحاولات المسموح، حاول مرة أخرى لاحقاً';
                case 'auth/network-request-failed':
                    return 'خطأ في الاتصال بالإنترنت';
                default:
                    return 'حدث خطأ غير متوقع، حاول مرة أخرى';
            }
        }

        async function handleLogin(event) {
            event.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            setLoading('login-form-element', true);

            try {
                const userCredential = await auth.signInWithEmailAndPassword(email, password);
                const user = userCredential.user;

                showMessage('تم تسجيل الدخول بنجاح!', 'success');

                localStorage.setItem('user', JSON.stringify({
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName
                }));

                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);

            } catch (error) {
                console.error('Login error:', error);
                showMessage(getErrorMessage(error.code), 'error');
            } finally {
                setLoading('login-form-element', false);
            }
        }

        async function handleRegister(event) {
            event.preventDefault();

            const name = document.getElementById('register-name').value;
            const email = document.getElementById('register-email').value;
            const phone = document.getElementById('register-phone').value;
            const password = document.getElementById('register-password').value;
            const confirmPassword = document.getElementById('register-confirm-password').value;

            if (password !== confirmPassword) {
                showMessage('كلمة المرور وتأكيد كلمة المرور غير متطابقتين', 'error');
                return;
            }

            setLoading('register-form-element', true);

            try {
                const userCredential = await auth.createUserWithEmailAndPassword(email, password);
                const user = userCredential.user;

                // Update user profile
                await user.updateProfile({
                    displayName: name
                });

                // Determine user role based on email
                const adminEmails = ['<EMAIL>'];
                const userRole = adminEmails.includes(email) ? 'admin' : 'user';

                // Save user data to Realtime Database with name as key
                await database.ref('users/' + name.replace(/\s+/g, '_')).set({
                    name: name,
                    email: email,
                    phone: phone,
                    createdAt: new Date().toISOString(),
                    role: userRole,
                    uid: user.uid,
                    permissions: userRole === 'admin' ? {
                        manageContent: true,
                        manageBranches: true,
                        manageUsers: true,
                        manageMessages: true,
                        manageGallery: true,
                        manageSettings: true
                    } : {
                        manageContent: false,
                        manageBranches: false,
                        manageUsers: false,
                        manageMessages: false,
                        manageGallery: false,
                        manageSettings: false
                    }
                });

                showMessage('تم إنشاء الحساب بنجاح!', 'success');

                localStorage.setItem('user', JSON.stringify({
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName
                }));

                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);

            } catch (error) {
                console.error('Registration error:', error);
                showMessage(getErrorMessage(error.code), 'error');
            } finally {
                setLoading('register-form-element', false);
            }
        }
    </script>
</body>
</html>
