<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام معلومات التواصل متعدد اللغات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .language-toggle {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .contact-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .contact-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار نظام معلومات التواصل متعدد اللغات</h1>
        
        <button class="language-toggle" onclick="toggleLanguage()">
            🌐 تبديل اللغة (Current: <span id="current-lang">العربية</span>)
        </button>

        <div class="test-section">
            <h2>📞 معلومات التواصل</h2>
            <div class="contact-info">
                <div class="contact-item">
                    <strong>عنوان القسم:</strong>
                    <span id="contact-title" data-ar="اتصل بنا" data-en="Contact Us">اتصل بنا</span>
                </div>
                
                <div class="contact-item">
                    <strong>عنوان معلومات التواصل:</strong>
                    <span id="contact-info-title" data-ar="معلومات التواصل" data-en="Contact Information">معلومات التواصل</span>
                </div>
                
                <div class="contact-item">
                    <strong data-ar="العنوان:" data-en="Address:">العنوان:</strong>
                    <span id="contact-address-display" data-ar="شارع الملك فهد، الرياض، المملكة العربية السعودية" data-en="King Fahd Street, Riyadh, Saudi Arabia">شارع الملك فهد، الرياض، المملكة العربية السعودية</span>
                </div>
                
                <div class="contact-item">
                    <strong data-ar="ساعات العمل:" data-en="Working Hours:">ساعات العمل:</strong>
                    <span id="contact-hours-display" data-ar="من السبت إلى الخميس 8:00 ص - 6:00 م" data-en="Saturday to Thursday 8:00 AM - 6:00 PM">من السبت إلى الخميس 8:00 ص - 6:00 م</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 اختبار الوظائف</h2>
            <button onclick="testDataAttributes()" style="margin: 5px; padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                اختبار خصائص البيانات
            </button>
            <button onclick="testLanguageSwitch()" style="margin: 5px; padding: 8px 15px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">
                اختبار تبديل اللغة
            </button>
            <button onclick="simulateFirebaseUpdate()" style="margin: 5px; padding: 8px 15px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
                محاكاة تحديث Firebase
            </button>
            
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 تعليمات الاختبار</h2>
            <ol>
                <li>اضغط على زر "تبديل اللغة" لاختبار التبديل بين العربية والإنجليزية</li>
                <li>تحقق من تغيير جميع النصوص بشكل صحيح</li>
                <li>اضغط على أزرار الاختبار للتحقق من الوظائف</li>
                <li>تأكد من عمل جميع العناصر بشكل صحيح</li>
            </ol>
        </div>
    </div>

    <script>
        let currentLanguage = 'ar';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            updateLanguage();
            document.getElementById('current-lang').textContent = currentLanguage === 'ar' ? 'العربية' : 'English';
        }

        function updateLanguage() {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            elements.forEach(element => {
                if (currentLanguage === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
        }

        function testDataAttributes() {
            const results = document.getElementById('test-results');
            let html = '<div class="status info"><h4>🔍 نتائج اختبار خصائص البيانات:</h4>';
            
            const elements = [
                'contact-title',
                'contact-info-title', 
                'contact-address-display',
                'contact-hours-display'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                const hasAr = element.hasAttribute('data-ar');
                const hasEn = element.hasAttribute('data-en');
                const status = hasAr && hasEn ? '✅' : '❌';
                html += `<p>${status} ${id}: العربية=${hasAr}, الإنجليزية=${hasEn}</p>`;
            });

            html += '</div>';
            results.innerHTML = html;
        }

        function testLanguageSwitch() {
            const results = document.getElementById('test-results');
            let html = '<div class="status success"><h4>🔄 اختبار تبديل اللغة:</h4>';
            
            // Test switching to English
            currentLanguage = 'en';
            updateLanguage();
            html += '<p>✅ تم التبديل إلى الإنجليزية</p>';
            
            // Test switching back to Arabic
            setTimeout(() => {
                currentLanguage = 'ar';
                updateLanguage();
                html += '<p>✅ تم التبديل إلى العربية</p>';
                html += '</div>';
                results.innerHTML = html;
                document.getElementById('current-lang').textContent = 'العربية';
            }, 1000);
        }

        function simulateFirebaseUpdate() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="status info"><h4>🔥 محاكاة تحديث Firebase...</h4></div>';
            
            // Simulate updating contact data
            setTimeout(() => {
                const mockData = {
                    titleAr: 'تواصل معنا',
                    titleEn: 'Get In Touch',
                    infoTitleAr: 'بيانات التواصل',
                    infoTitleEn: 'Contact Details',
                    addressAr: 'العنوان المحدث - الرياض، السعودية',
                    addressEn: 'Updated Address - Riyadh, Saudi Arabia',
                    hoursAr: 'الأحد إلى الخميس 9:00 ص - 5:00 م',
                    hoursEn: 'Sunday to Thursday 9:00 AM - 5:00 PM'
                };

                // Update elements
                document.getElementById('contact-title').setAttribute('data-ar', mockData.titleAr);
                document.getElementById('contact-title').setAttribute('data-en', mockData.titleEn);
                document.getElementById('contact-info-title').setAttribute('data-ar', mockData.infoTitleAr);
                document.getElementById('contact-info-title').setAttribute('data-en', mockData.infoTitleEn);
                document.getElementById('contact-address-display').setAttribute('data-ar', mockData.addressAr);
                document.getElementById('contact-address-display').setAttribute('data-en', mockData.addressEn);
                document.getElementById('contact-hours-display').setAttribute('data-ar', mockData.hoursAr);
                document.getElementById('contact-hours-display').setAttribute('data-en', mockData.hoursEn);

                updateLanguage();

                results.innerHTML = '<div class="status success"><h4>✅ تم تحديث البيانات بنجاح!</h4><p>تم تحديث جميع معلومات التواصل بالبيانات الجديدة</p></div>';
            }, 1500);
        }

        // Initialize
        updateLanguage();
    </script>
</body>
</html>
