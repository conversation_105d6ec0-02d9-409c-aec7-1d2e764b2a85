<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لنظام إدارة المحتوى متعدد اللغات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .language-toggle {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .language-toggle:hover {
            background: #0056b3;
        }
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 15px 0;
        }
        .content-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .content-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .content-item p {
            margin: 5px 0;
            color: #666;
        }
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .test-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn.primary { background: #007bff; color: white; }
        .test-btn.success { background: #28a745; color: white; }
        .test-btn.warning { background: #ffc107; color: black; }
        .test-btn.info { background: #17a2b8; color: white; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار شامل لنظام إدارة المحتوى متعدد اللغات</h1>
        
        <button class="language-toggle" onclick="toggleLanguage()">
            🌐 تبديل اللغة (Current: <span id="current-lang">العربية</span>)
        </button>

        <!-- Contact Information Section -->
        <div class="test-section">
            <h2>📞 معلومات التواصل</h2>
            <div class="content-grid">
                <div class="content-item">
                    <h4 id="contact-title" data-ar="اتصل بنا" data-en="Contact Us">اتصل بنا</h4>
                    <p><strong id="contact-info-title" data-ar="معلومات التواصل" data-en="Contact Information">معلومات التواصل</strong></p>
                    <p><strong data-ar="العنوان:" data-en="Address:">العنوان:</strong> <span id="contact-address" data-ar="شارع الملك فهد، الرياض، المملكة العربية السعودية" data-en="King Fahd Street, Riyadh, Saudi Arabia">شارع الملك فهد، الرياض، المملكة العربية السعودية</span></p>
                    <p><strong data-ar="ساعات العمل:" data-en="Working Hours:">ساعات العمل:</strong> <span id="contact-hours" data-ar="من السبت إلى الخميس 8:00 ص - 6:00 م" data-en="Saturday to Thursday 8:00 AM - 6:00 PM">من السبت إلى الخميس 8:00 ص - 6:00 م</span></p>
                </div>
            </div>
        </div>

        <!-- Branches Section -->
        <div class="test-section">
            <h2>🏪 الفروع</h2>
            <div class="content-grid" id="branches-container">
                <div class="content-item">
                    <h4 id="branch-1-name" data-ar="فرع الرياض" data-en="Riyadh Branch">فرع الرياض</h4>
                    <p><strong data-ar="العنوان:" data-en="Address:">العنوان:</strong> <span id="branch-1-address" data-ar="شارع الملك فهد، الرياض" data-en="King Fahd Street, Riyadh">شارع الملك فهد، الرياض</span></p>
                    <p><strong data-ar="الهاتف:" data-en="Phone:">الهاتف:</strong> 0112345678</p>
                </div>
                <div class="content-item">
                    <h4 id="branch-2-name" data-ar="فرع جدة" data-en="Jeddah Branch">فرع جدة</h4>
                    <p><strong data-ar="العنوان:" data-en="Address:">العنوان:</strong> <span id="branch-2-address" data-ar="شارع التحلية، جدة" data-en="Tahlia Street, Jeddah">شارع التحلية، جدة</span></p>
                    <p><strong data-ar="الهاتف:" data-en="Phone:">الهاتف:</strong> 0123456789</p>
                </div>
            </div>
        </div>

        <!-- Gallery Images Section -->
        <div class="test-section">
            <h2>🖼️ معرض الصور</h2>
            <div class="content-grid" id="gallery-container">
                <div class="content-item">
                    <h4 id="gallery-1-title" data-ar="تركيب زجاج أمامي" data-en="Front Glass Installation">تركيب زجاج أمامي</h4>
                    <p id="gallery-1-desc" data-ar="تركيب زجاج أمامي عالي الجودة للسيارات" data-en="High quality front glass installation for cars">تركيب زجاج أمامي عالي الجودة للسيارات</p>
                    <p><strong data-ar="الفئة:" data-en="Category:">الفئة:</strong> <span data-ar="التركيب" data-en="Installation">التركيب</span></p>
                </div>
                <div class="content-item">
                    <h4 id="gallery-2-title" data-ar="إصلاح زجاج جانبي" data-en="Side Glass Repair">إصلاح زجاج جانبي</h4>
                    <p id="gallery-2-desc" data-ar="إصلاح احترافي للزجاج الجانبي" data-en="Professional side glass repair">إصلاح احترافي للزجاج الجانبي</p>
                    <p><strong data-ar="الفئة:" data-en="Category:">الفئة:</strong> <span data-ar="الإصلاح" data-en="Repair">الإصلاح</span></p>
                </div>
            </div>
        </div>

        <!-- Service Images Section -->
        <div class="test-section">
            <h2>🔧 صور الخدمات</h2>
            <div class="content-grid" id="services-container">
                <div class="content-item">
                    <h4 id="service-1-title" data-ar="خدمات زجاج السيارات" data-en="Car Glass Services">خدمات زجاج السيارات</h4>
                    <p id="service-1-desc" data-ar="أفضل خدمات تركيب وإصلاح زجاج السيارات" data-en="Best car glass installation and repair services">أفضل خدمات تركيب وإصلاح زجاج السيارات</p>
                </div>
                <div class="content-item">
                    <h4 id="service-2-title" data-ar="تركيب زجاج السيارات" data-en="Car Glass Installation">تركيب زجاج السيارات</h4>
                    <p id="service-2-desc" data-ar="تركيب احترافي بأعلى معايير الجودة" data-en="Professional installation with highest quality standards">تركيب احترافي بأعلى معايير الجودة</p>
                </div>
                <div class="content-item">
                    <h4 id="service-3-title" data-ar="إصلاح زجاج السيارات" data-en="Car Glass Repair">إصلاح زجاج السيارات</h4>
                    <p id="service-3-desc" data-ar="إصلاح سريع وفعال لجميع أنواع السيارات" data-en="Fast and effective repair for all car types">إصلاح سريع وفعال لجميع أنواع السيارات</p>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h2>🔧 أدوات الاختبار</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testDataAttributes()">اختبار خصائص البيانات</button>
                <button class="test-btn success" onclick="testLanguageSwitch()">اختبار تبديل اللغة</button>
                <button class="test-btn warning" onclick="simulateDataUpdate()">محاكاة تحديث البيانات</button>
                <button class="test-btn info" onclick="testAllSections()">اختبار جميع الأقسام</button>
            </div>
            
            <div id="test-results"></div>
        </div>

        <!-- Instructions -->
        <div class="test-section">
            <h2>📋 تعليمات الاختبار</h2>
            <ol>
                <li>اضغط على زر "تبديل اللغة" لاختبار التبديل بين العربية والإنجليزية</li>
                <li>تحقق من تغيير جميع النصوص في جميع الأقسام بشكل صحيح</li>
                <li>اضغط على أزرار الاختبار للتحقق من الوظائف المختلفة</li>
                <li>تأكد من عمل جميع العناصر في جميع الأقسام بشكل صحيح</li>
                <li>اختبر محاكاة تحديث البيانات للتأكد من التحديث الفوري</li>
            </ol>
        </div>
    </div>

    <script>
        let currentLanguage = 'ar';

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            updateLanguage();
            document.getElementById('current-lang').textContent = currentLanguage === 'ar' ? 'العربية' : 'English';
        }

        function updateLanguage() {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            elements.forEach(element => {
                if (currentLanguage === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
        }

        function testDataAttributes() {
            const results = document.getElementById('test-results');
            let html = '<div class="status info"><h4>🔍 نتائج اختبار خصائص البيانات:</h4>';
            
            const sections = [
                { name: 'معلومات التواصل', elements: ['contact-title', 'contact-info-title', 'contact-address', 'contact-hours'] },
                { name: 'الفروع', elements: ['branch-1-name', 'branch-1-address', 'branch-2-name', 'branch-2-address'] },
                { name: 'معرض الصور', elements: ['gallery-1-title', 'gallery-1-desc', 'gallery-2-title', 'gallery-2-desc'] },
                { name: 'صور الخدمات', elements: ['service-1-title', 'service-1-desc', 'service-2-title', 'service-2-desc', 'service-3-title', 'service-3-desc'] }
            ];

            sections.forEach(section => {
                html += `<h5>${section.name}:</h5>`;
                section.elements.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        const hasAr = element.hasAttribute('data-ar');
                        const hasEn = element.hasAttribute('data-en');
                        const status = hasAr && hasEn ? '✅' : '❌';
                        html += `<p>${status} ${id}: العربية=${hasAr}, الإنجليزية=${hasEn}</p>`;
                    }
                });
            });

            html += '</div>';
            results.innerHTML = html;
        }

        function testLanguageSwitch() {
            const results = document.getElementById('test-results');
            let html = '<div class="status success"><h4>🔄 اختبار تبديل اللغة:</h4>';
            
            // Test switching to English
            currentLanguage = 'en';
            updateLanguage();
            html += '<p>✅ تم التبديل إلى الإنجليزية</p>';
            
            // Test switching back to Arabic
            setTimeout(() => {
                currentLanguage = 'ar';
                updateLanguage();
                html += '<p>✅ تم التبديل إلى العربية</p>';
                html += '</div>';
                results.innerHTML = html;
                document.getElementById('current-lang').textContent = 'العربية';
            }, 1000);
        }

        function simulateDataUpdate() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="status warning"><h4>🔄 محاكاة تحديث البيانات...</h4></div>';
            
            setTimeout(() => {
                // Update contact info
                document.getElementById('contact-address').setAttribute('data-ar', 'العنوان المحدث - الرياض، السعودية');
                document.getElementById('contact-address').setAttribute('data-en', 'Updated Address - Riyadh, Saudi Arabia');
                
                // Update branch info
                document.getElementById('branch-1-name').setAttribute('data-ar', 'فرع الرياض المحدث');
                document.getElementById('branch-1-name').setAttribute('data-en', 'Updated Riyadh Branch');
                
                // Update gallery info
                document.getElementById('gallery-1-title').setAttribute('data-ar', 'تركيب زجاج محدث');
                document.getElementById('gallery-1-title').setAttribute('data-en', 'Updated Glass Installation');
                
                // Update service info
                document.getElementById('service-1-title').setAttribute('data-ar', 'خدمات محدثة');
                document.getElementById('service-1-title').setAttribute('data-en', 'Updated Services');

                updateLanguage();

                results.innerHTML = '<div class="status success"><h4>✅ تم تحديث البيانات بنجاح!</h4><p>تم تحديث جميع الأقسام بالبيانات الجديدة</p></div>';
            }, 1500);
        }

        function testAllSections() {
            const results = document.getElementById('test-results');
            let html = '<div class="status info"><h4>🧪 اختبار شامل لجميع الأقسام:</h4>';
            
            const sections = ['معلومات التواصل', 'الفروع', 'معرض الصور', 'صور الخدمات'];
            
            sections.forEach((section, index) => {
                setTimeout(() => {
                    html += `<p>✅ تم اختبار ${section}</p>`;
                    if (index === sections.length - 1) {
                        html += '<p><strong>🎉 تم اختبار جميع الأقسام بنجاح!</strong></p></div>';
                    }
                    results.innerHTML = html;
                }, (index + 1) * 500);
            });
        }

        // Initialize
        updateLanguage();
    </script>
</body>
</html>
