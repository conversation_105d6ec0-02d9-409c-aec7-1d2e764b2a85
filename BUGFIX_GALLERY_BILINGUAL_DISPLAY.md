# إصلاح عرض النصوص الإنجليزية في معرض الصور

## المشكلة المكتشفة
عند تبديل اللغة إلى الإنجليزية في الصفحة الرئيسية، صور معرض الصور لا تظهر النصوص الإنجليزية (العناوين والأوصاف) بل تبقى تظهر النصوص العربية.

## سبب المشكلة
كان هناك نظامان منفصلان لعرض الصور:

1. **صور الخدمات** (`serviceImages`) - تدعم اللغتين بشكل صحيح
2. **معرض الصور الرئيسي** (`galleryImages`) - لا يدعم اللغتين بشكل صحيح

المشكلة كانت في ملف `dynamic-content.js` الذي يدير معرض الصور الرئيسي، حيث كان يستخدم `image.title` و `image.description` مباشرة بدلاً من دعم الحقول متعددة اللغات.

## الإصلاحات المطبقة

### 1. تحديث وظيفة `createMainGalleryItem` في dynamic-content.js

#### قبل الإصلاح:
```javascript
div.innerHTML = `
    <div class="gallery-image-container">
        <img src="${image.url}" alt="${image.title}" class="gallery-main-image">
        ${image.featured ? '<div class="featured-badge">مميزة</div>' : ''}
    </div>
    <div class="gallery-item-info">
        <h4>${image.title}</h4>
        <p>${image.description}</p>
        <div class="gallery-item-meta">
            <span class="category-badge">${categoryName}</span>
        </div>
        <div class="gallery-item-actions">
            <span class="view-icon">👁️</span>
            <span class="view-text">عرض التفاصيل</span>
        </div>
    </div>
`;
```

#### بعد الإصلاح:
```javascript
// Get current language for display
const currentLang = this.getCurrentLanguage();

// Get bilingual content
const titleToShow = currentLang === 'en' && image.titleEn ? image.titleEn : (image.titleAr || image.title || 'صورة');
const descToShow = currentLang === 'en' && image.descriptionEn ? image.descriptionEn : (image.descriptionAr || image.description || 'وصف الصورة');
const featuredText = currentLang === 'en' ? 'Featured' : 'مميزة';
const viewText = currentLang === 'en' ? 'View Details' : 'عرض التفاصيل';

div.innerHTML = `
    <div class="gallery-image-container">
        <img src="${image.url}" alt="${titleToShow}" class="gallery-main-image">
        ${image.featured ? `<div class="featured-badge">${featuredText}</div>` : ''}
    </div>
    <div class="gallery-item-info">
        <h4 data-ar="${image.titleAr || image.title || 'صورة'}" data-en="${image.titleEn || 'Image'}">${titleToShow}</h4>
        <p data-ar="${image.descriptionAr || image.description || 'وصف الصورة'}" data-en="${image.descriptionEn || 'Image description'}">${descToShow}</p>
        <div class="gallery-item-meta">
            <span class="category-badge">${categoryName}</span>
        </div>
        <div class="gallery-item-actions">
            <span class="view-icon">👁️</span>
            <span class="view-text" data-ar="عرض التفاصيل" data-en="View Details">${viewText}</span>
        </div>
    </div>
`;
```

### 2. إضافة وظيفة `getCurrentLanguage`

```javascript
getCurrentLanguage() {
    return document.documentElement.getAttribute('data-lang') || localStorage.getItem('language') || 'ar';
}
```

### 3. تحديث وظيفة `getCategoryDisplayName` لدعم اللغتين

#### قبل الإصلاح:
```javascript
getCategoryDisplayName(category) {
    const categories = {
        'services': 'خدماتنا',
        'installation': 'التركيب',
        'repair': 'الإصلاح',
        'products': 'منتجاتنا'
    };
    return categories[category] || 'عام';
}
```

#### بعد الإصلاح:
```javascript
getCategoryDisplayName(category) {
    const currentLang = this.getCurrentLanguage();
    const categories = {
        'services': { ar: 'خدماتنا', en: 'Our Services' },
        'installation': { ar: 'التركيب', en: 'Installation' },
        'repair': { ar: 'الإصلاح', en: 'Repair' },
        'products': { ar: 'منتجاتنا', en: 'Our Products' },
        'general': { ar: 'عام', en: 'General' }
    };
    
    const categoryData = categories[category] || categories['general'];
    return currentLang === 'en' ? categoryData.en : categoryData.ar;
}
```

### 4. إضافة وظيفة `updateGalleryLanguage`

```javascript
updateGalleryLanguage() {
    try {
        const galleryItems = document.querySelectorAll('.gallery-main-item');
        const currentLang = this.getCurrentLanguage();
        
        galleryItems.forEach(item => {
            // Update title
            const titleElement = item.querySelector('h4[data-ar][data-en]');
            if (titleElement) {
                const titleText = currentLang === 'en' ? titleElement.getAttribute('data-en') : titleElement.getAttribute('data-ar');
                titleElement.textContent = titleText;
            }
            
            // Update description
            const descElement = item.querySelector('p[data-ar][data-en]');
            if (descElement) {
                const descText = currentLang === 'en' ? descElement.getAttribute('data-en') : descElement.getAttribute('data-ar');
                descElement.textContent = descText;
            }
            
            // Update view text
            const viewTextElement = item.querySelector('.view-text[data-ar][data-en]');
            if (viewTextElement) {
                const viewText = currentLang === 'en' ? viewTextElement.getAttribute('data-en') : viewTextElement.getAttribute('data-ar');
                viewTextElement.textContent = viewText;
            }
            
            // Update featured badge
            const featuredBadge = item.querySelector('.featured-badge');
            if (featuredBadge) {
                featuredBadge.textContent = currentLang === 'en' ? 'Featured' : 'مميزة';
            }
            
            // Update category badge
            const categoryBadge = item.querySelector('.category-badge');
            if (categoryBadge) {
                const category = item.getAttribute('data-category');
                categoryBadge.textContent = this.getCategoryDisplayName(category);
            }
        });
        
        console.log('Gallery language updated to:', currentLang);
    } catch (error) {
        console.error('Error updating gallery language:', error);
    }
}
```

### 5. ربط تحديث المعرض بنظام تبديل اللغة

في `index.html`، تم تحديث وظيفة `updateLanguage`:

```javascript
// Update body class for styling
document.body.className = document.body.className.replace(/lang-\w+/, '');
document.body.classList.add(`lang-${currentLanguage}`);

// Update gallery language if dynamic content manager is available
if (window.dynamicContentManager && typeof window.dynamicContentManager.updateGalleryLanguage === 'function') {
    window.dynamicContentManager.updateGalleryLanguage();
}

console.log(`Language switched to: ${currentLanguage}`);
```

## المميزات الجديدة

### ✅ دعم كامل للغتين في معرض الصور:
- **العناوين**: `titleAr` / `titleEn`
- **الأوصاف**: `descriptionAr` / `descriptionEn`
- **شارات الفئات**: مترجمة بالكامل
- **نص "عرض التفاصيل"**: "View Details" / "عرض التفاصيل"
- **شارة "مميزة"**: "Featured" / "مميزة"

### ✅ التوافق العكسي:
- يدعم البيانات القديمة (`title`, `description`)
- يعمل مع البيانات الجديدة (`titleAr`, `titleEn`, `descriptionAr`, `descriptionEn`)
- لا يؤثر على الصور الموجودة

### ✅ تحديث فوري:
- عند تبديل اللغة، تتحدث جميع النصوص فوراً
- لا حاجة لإعادة تحميل الصفحة
- تجربة مستخدم سلسة

## اختبار الإصلاح

### خطوات الاختبار:
1. **فتح الصفحة الرئيسية**
2. **التأكد من وجود صور في معرض الصور**
3. **الضغط على زر تبديل اللغة 🌐**
4. **التحقق من تحديث النصوص فوراً**

### النتائج المتوقعة:

#### 🇸🇦 عند اختيار العربية:
- العناوين تظهر بالعربية
- الأوصاف تظهر بالعربية
- الفئات: "خدماتنا"، "التركيب"، "الإصلاح"
- نص الإجراء: "عرض التفاصيل"
- شارة مميزة: "مميزة"

#### 🇺🇸 عند اختيار الإنجليزية:
- العناوين تظهر بالإنجليزية
- الأوصاف تظهر بالإنجليزية
- الفئات: "Our Services"، "Installation"، "Repair"
- نص الإجراء: "View Details"
- شارة مميزة: "Featured"

## الملفات المحدثة

### dynamic-content.js
- **السطور 749-789**: تحديث `createMainGalleryItem` لدعم اللغتين
- **السطور 791-793**: إضافة `getCurrentLanguage`
- **السطور 795-807**: تحديث `getCategoryDisplayName` لدعم اللغتين
- **السطور 1114-1161**: إضافة `updateGalleryLanguage`

### index.html
- **السطور 685-687**: ربط تحديث المعرض بنظام تبديل اللغة

## التحسينات الإضافية

### 🔄 تحديث تلقائي:
- المعرض يتحدث تلقائياً عند تبديل اللغة
- لا حاجة لتدخل المستخدم
- تجربة سلسة ومتسقة

### 🌐 دعم شامل للغتين:
- جميع عناصر المعرض تدعم اللغتين
- ترجمة كاملة لجميع النصوص
- تصميم متجاوب مع اتجاه النص

### 📱 تجربة مستخدم محسنة:
- تحديث فوري بدون تأخير
- انتقالات سلسة بين اللغات
- حفظ تفضيلات اللغة

## الخلاصة

تم إصلاح مشكلة عرض النصوص الإنجليزية في معرض الصور بنجاح! الآن:

- **✅ معرض الصور يدعم اللغتين بالكامل**
- **✅ تحديث فوري عند تبديل اللغة**
- **✅ التوافق العكسي مع البيانات القديمة**
- **✅ تجربة مستخدم متسقة وسلسة**
- **✅ ترجمة شاملة لجميع العناصر**

النظام الآن يوفر تجربة متكاملة ومتسقة للمستخدمين باللغتين العربية والإنجليزية! 🎉

## ملاحظات للمطورين

### نصائح للصيانة:
1. **عند إضافة صور جديدة**: تأكد من ملء الحقول باللغتين
2. **عند تحديث النصوص**: استخدم `titleAr/titleEn` و `descriptionAr/descriptionEn`
3. **عند إضافة فئات جديدة**: أضفها في `getCategoryDisplayName`
4. **اختبار دوري**: تأكد من عمل تبديل اللغة بشكل صحيح

### أدوات التشخيص:
- **وحدة التحكم**: مراقبة رسائل `Gallery language updated to:`
- **Developer Tools**: فحص خصائص `data-ar` و `data-en`
- **Network Tab**: مراقبة تحميل البيانات من Firebase
