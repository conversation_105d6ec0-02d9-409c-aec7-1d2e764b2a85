/**
 * Gallery Manager - إدارة معرض الصور
 * يدير عرض وتصفية الصور في معرض الصور الرئيسي
 */

class GalleryManager {
    constructor() {
        this.currentCategory = 'all';
        this.currentView = 'grid';
        this.images = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDefaultImages();
        this.updateCounts();
        this.hideLoading();
    }

    bindEvents() {
        // Category filters
        document.querySelectorAll('.gallery-category').forEach(category => {
            category.addEventListener('click', (e) => this.handleCategoryClick(e));
        });

        // View toggles
        document.getElementById('grid-view-btn')?.addEventListener('click', () => this.setView('grid'));
        document.getElementById('list-view-btn')?.addEventListener('click', () => this.setView('list'));

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    loadDefaultImages() {
        this.images = [
            {
                src: 'img/car2.png',
                title: 'خدمات زجاج السيارات',
                description: 'خدمات متميزة لزجاج السيارات',
                category: 'services'
            },
            {
                src: 'img/car4.png',
                title: 'تركيب زجاج السيارات',
                description: 'تركيب احترافي بأعلى جودة',
                category: 'installation'
            },
            {
                src: 'img/car6.png',
                title: 'إصلاح زجاج السيارات',
                description: 'إصلاح سريع وموثوق',
                category: 'repair'
            }
        ];
    }

    handleCategoryClick(event) {
        const category = event.currentTarget;
        const categoryName = category.dataset.category;

        // Update active category
        document.querySelectorAll('.gallery-category').forEach(cat => {
            cat.classList.remove('active');
        });
        category.classList.add('active');

        // Update title
        const categoryTitle = category.querySelector('.category-name').textContent;
        document.querySelector('.current-category-title').textContent = categoryTitle;

        // Filter images
        this.currentCategory = categoryName;
        this.filterImages(categoryName);
        this.updateCounts();
    }

    filterImages(category) {
        const items = document.querySelectorAll('.gallery-main-item');
        
        items.forEach((item, index) => {
            const shouldShow = category === 'all' || item.dataset.category === category;
            
            if (shouldShow) {
                item.style.display = 'block';
                item.style.animation = `fadeInUp 0.6s ease-out ${index * 0.1}s both`;
            } else {
                item.style.display = 'none';
            }
        });
    }

    setView(viewType) {
        const grid = document.getElementById('main-gallery-grid');
        const gridBtn = document.getElementById('grid-view-btn');
        const listBtn = document.getElementById('list-view-btn');

        if (viewType === 'list') {
            grid.classList.add('list-view');
            listBtn.classList.add('active');
            gridBtn.classList.remove('active');
        } else {
            grid.classList.remove('list-view');
            gridBtn.classList.add('active');
            listBtn.classList.remove('active');
        }

        this.currentView = viewType;
    }

    updateCounts() {
        const totalElement = document.getElementById('total-images');
        const selectedElement = document.getElementById('selected-category-count');
        
        if (!totalElement || !selectedElement) return;

        const allItems = document.querySelectorAll('.gallery-main-item');
        const total = allItems.length;
        
        totalElement.textContent = total;

        if (this.currentCategory === 'all') {
            selectedElement.textContent = total;
        } else {
            const categoryItems = document.querySelectorAll(`.gallery-main-item[data-category="${this.currentCategory}"]`);
            selectedElement.textContent = categoryItems.length;
        }

        // Animate numbers
        this.animateNumber(totalElement);
        this.animateNumber(selectedElement);
    }

    animateNumber(element) {
        element.style.transform = 'scale(1.2)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 200);
    }

    hideLoading() {
        const loading = document.getElementById('gallery-loading');
        if (loading) {
            loading.style.display = 'none';
        }
    }

    handleKeyboard(event) {
        // Arrow keys for category navigation
        if (event.key === 'ArrowRight' || event.key === 'ArrowLeft') {
            const categories = document.querySelectorAll('.gallery-category');
            const activeIndex = Array.from(categories).findIndex(cat => cat.classList.contains('active'));
            
            let newIndex;
            if (event.key === 'ArrowRight') {
                newIndex = (activeIndex + 1) % categories.length;
            } else {
                newIndex = activeIndex > 0 ? activeIndex - 1 : categories.length - 1;
            }
            
            categories[newIndex].click();
        }

        // V key for view toggle
        if (event.key === 'v' || event.key === 'V') {
            this.setView(this.currentView === 'grid' ? 'list' : 'grid');
        }
    }

    // Add new image (for Firebase integration)
    addImage(imageData) {
        this.images.push(imageData);
        this.renderImages();
        this.updateCounts();
    }

    // Remove image (for Firebase integration)
    removeImage(imageId) {
        this.images = this.images.filter(img => img.id !== imageId);
        this.renderImages();
        this.updateCounts();
    }

    // Render images dynamically
    renderImages() {
        const container = document.getElementById('main-gallery-grid');
        const loadingElement = container.querySelector('.gallery-loading');
        
        // Clear existing images except loading
        const existingItems = container.querySelectorAll('.gallery-main-item');
        existingItems.forEach(item => item.remove());

        // Add images
        this.images.forEach(image => {
            const item = this.createImageElement(image);
            container.appendChild(item);
        });

        // Filter current category
        this.filterImages(this.currentCategory);
    }

    createImageElement(image) {
        const div = document.createElement('div');
        div.className = 'gallery-main-item';
        div.dataset.category = image.category;
        div.onclick = () => openImageModal(image.src, image.title, image.description);

        div.innerHTML = `
            <div class="gallery-image-container">
                <img src="${image.src}" alt="${image.title}" class="gallery-main-image">
            </div>
            <div class="gallery-item-info">
                <h4>${image.title}</h4>
                <p>${image.description}</p>
                <div class="gallery-item-actions">
                    <span class="view-icon">👁️</span>
                    <span class="view-text">عرض التفاصيل</span>
                </div>
            </div>
        `;

        return div;
    }

    // Get current filter statistics
    getStats() {
        return {
            total: this.images.length,
            categories: {
                services: this.images.filter(img => img.category === 'services').length,
                installation: this.images.filter(img => img.category === 'installation').length,
                repair: this.images.filter(img => img.category === 'repair').length,
                products: this.images.filter(img => img.category === 'products').length
            },
            currentCategory: this.currentCategory,
            currentView: this.currentView
        };
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('main-gallery-grid')) {
        window.galleryManager = new GalleryManager();
        console.log('🖼️ Gallery Manager initialized');
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GalleryManager;
}
