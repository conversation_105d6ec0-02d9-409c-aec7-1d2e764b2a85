/**
 * Image Modal Manager - إدارة النافذة المنبثقة للصور
 * يدير عرض الصور بحجم كامل مع إمكانيات التنقل والتحكم
 */

class ImageModal {
    constructor() {
        this.isOpen = false;
        this.currentImageIndex = 0;
        this.images = [];
        this.init();
    }

    init() {
        this.createModal();
        this.bindEvents();
    }

    createModal() {
        // Check if modal already exists
        if (document.getElementById('imageModal')) {
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'image-modal';
        modal.id = 'imageModal';
        modal.innerHTML = `
            <div class="image-modal-content">
                <span class="image-modal-close" onclick="imageModal.close()">&times;</span>
                <div class="image-modal-nav">
                    <button class="modal-nav-btn prev" onclick="imageModal.previous()">‹</button>
                    <button class="modal-nav-btn next" onclick="imageModal.next()">›</button>
                </div>
                <img class="image-modal-img" id="modalImage" src="" alt="">
                <div class="image-modal-info">
                    <h3 id="modalTitle"></h3>
                    <p id="modalDescription"></p>
                    <div class="modal-counter">
                        <span id="modalCounter">1 / 1</span>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    bindEvents() {
        // Close on background click
        document.addEventListener('click', (e) => {
            if (e.target.id === 'imageModal') {
                this.close();
            }
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (!this.isOpen) return;

            switch (e.key) {
                case 'Escape':
                    this.close();
                    break;
                case 'ArrowLeft':
                    this.previous();
                    break;
                case 'ArrowRight':
                    this.next();
                    break;
                case ' ':
                    e.preventDefault();
                    this.next();
                    break;
            }
        });

        // Touch/swipe support for mobile
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', (e) => {
            if (!this.isOpen) return;
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', (e) => {
            if (!this.isOpen) return;
            endX = e.changedTouches[0].clientX;
            this.handleSwipe();
        });

        this.handleSwipe = () => {
            const threshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > threshold) {
                if (diff > 0) {
                    this.next(); // Swipe left - next image
                } else {
                    this.previous(); // Swipe right - previous image
                }
            }
        };
    }

    open(imageSrc, title, description, imageIndex = 0) {
        const modal = document.getElementById('imageModal');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalDescription = document.getElementById('modalDescription');

        if (!modal || !modalImage || !modalTitle || !modalDescription) {
            console.error('Modal elements not found');
            return;
        }

        // Set image data
        modalImage.src = imageSrc;
        modalImage.alt = title;
        modalTitle.textContent = title;
        modalDescription.textContent = description;

        // Update current index
        this.currentImageIndex = imageIndex;
        this.updateCounter();
        this.updateNavigation();

        // Show modal
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        this.isOpen = true;

        // Add animation
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);

        // Preload adjacent images
        this.preloadAdjacentImages();
    }

    close() {
        const modal = document.getElementById('imageModal');
        if (!modal) return;

        modal.classList.remove('active');
        this.isOpen = false;

        setTimeout(() => {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }, 300);
    }

    next() {
        if (this.images.length <= 1) return;

        this.currentImageIndex = (this.currentImageIndex + 1) % this.images.length;
        this.updateImage();
    }

    previous() {
        if (this.images.length <= 1) return;

        this.currentImageIndex = this.currentImageIndex > 0 
            ? this.currentImageIndex - 1 
            : this.images.length - 1;
        this.updateImage();
    }

    updateImage() {
        const currentImage = this.images[this.currentImageIndex];
        if (!currentImage) return;

        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');
        const modalDescription = document.getElementById('modalDescription');

        // Add fade effect
        modalImage.style.opacity = '0';
        
        setTimeout(() => {
            modalImage.src = currentImage.src;
            modalImage.alt = currentImage.title;
            modalTitle.textContent = currentImage.title;
            modalDescription.textContent = currentImage.description;
            
            modalImage.style.opacity = '1';
        }, 150);

        this.updateCounter();
        this.preloadAdjacentImages();
    }

    updateCounter() {
        const counter = document.getElementById('modalCounter');
        if (counter && this.images.length > 1) {
            counter.textContent = `${this.currentImageIndex + 1} / ${this.images.length}`;
            counter.style.display = 'block';
        } else if (counter) {
            counter.style.display = 'none';
        }
    }

    updateNavigation() {
        const prevBtn = document.querySelector('.modal-nav-btn.prev');
        const nextBtn = document.querySelector('.modal-nav-btn.next');

        if (this.images.length <= 1) {
            if (prevBtn) prevBtn.style.display = 'none';
            if (nextBtn) nextBtn.style.display = 'none';
        } else {
            if (prevBtn) prevBtn.style.display = 'block';
            if (nextBtn) nextBtn.style.display = 'block';
        }
    }

    preloadAdjacentImages() {
        if (this.images.length <= 1) return;

        // Preload next image
        const nextIndex = (this.currentImageIndex + 1) % this.images.length;
        const nextImage = new Image();
        nextImage.src = this.images[nextIndex].src;

        // Preload previous image
        const prevIndex = this.currentImageIndex > 0 
            ? this.currentImageIndex - 1 
            : this.images.length - 1;
        const prevImage = new Image();
        prevImage.src = this.images[prevIndex].src;
    }

    setImages(images) {
        this.images = images;
        this.updateNavigation();
    }

    getCurrentImage() {
        return this.images[this.currentImageIndex];
    }

    getImageIndex(imageSrc) {
        return this.images.findIndex(img => img.src === imageSrc);
    }
}

// Global functions for backward compatibility
function openImageModal(imageSrc, title, description) {
    // Get all visible gallery images for navigation
    const visibleItems = Array.from(document.querySelectorAll('.gallery-main-item'))
        .filter(item => item.style.display !== 'none')
        .map(item => {
            const img = item.querySelector('.gallery-main-image');
            const info = item.querySelector('.gallery-item-info');
            return {
                src: img.src,
                title: info.querySelector('h4').textContent,
                description: info.querySelector('p').textContent
            };
        });

    const imageIndex = visibleItems.findIndex(img => img.src === imageSrc);
    
    window.imageModal.setImages(visibleItems);
    window.imageModal.open(imageSrc, title, description, imageIndex);
}

function closeImageModal() {
    window.imageModal.close();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.imageModal = new ImageModal();
    console.log('🖼️ Image Modal initialized');
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ImageModal;
}
