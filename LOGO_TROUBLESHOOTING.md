# دليل استكشاف أخطاء اللوجو - شركة السلامات

## المشكلة المحتملة
عند رفع اللوجو من لوحة الأدمن، قد لا يظهر بشكل صحيح في الصفحة الرئيسية.

## خطوات استكشاف الأخطاء

### 1. فحص Console المتصفح
افتح Developer Tools (F12) وتحقق من وجود أخطاء في Console:

```javascript
// في لوحة الأدمن، افتح Console واكتب:
console.log('Testing admin panel...');

// في الصفحة الرئيسية، افتح Console واكتب:
testLogo(); // لفحص حالة اللوجو
forceShowLogo(); // لإظهار لوجو تجريبي
```

### 2. فحص Firebase Connection
```javascript
// في Console لوحة الأدمن:
console.log('Firebase:', typeof firebase !== 'undefined');
console.log('Database:', adminPanel.database);

// في Console الصفحة الرئيسية:
console.log('Firebase:', typeof firebase !== 'undefined');
```

### 3. فحص بيانات اللوجو في Firebase
```javascript
// في Console أي من الصفحتين:
firebase.database().ref('siteLogo').once('value').then(snapshot => {
    console.log('Logo data:', snapshot.val());
});
```

### 4. فحص عناصر DOM
```javascript
// في لوحة الأدمن:
console.log('Logo file input:', document.getElementById('logo-file'));
console.log('Logo preview:', document.getElementById('current-logo-preview'));
console.log('Logo placeholder:', document.getElementById('logo-placeholder'));

// في الصفحة الرئيسية:
console.log('Company logo element:', document.getElementById('company-logo'));
```

### 5. اختبار رفع اللوجو خطوة بخطوة

#### في لوحة الأدمن:
1. افتح قسم "إدارة اللوجو"
2. اختر صورة صغيرة (أقل من 2MB)
3. تأكد من تفعيل "عرض اللوجو في الموقع"
4. اضغط "حفظ اللوجو"
5. راقب Console للرسائل

#### الرسائل المتوقعة في Console:
```
🖼️ Starting logo upload process...
📋 Form elements found: {fileInput: true, altInput: true, enabledCheckbox: true}
📁 File selected: {name: "logo.png", size: 15420, type: "image/png"}
✅ File validation passed
🔄 Converting file to Base64...
✅ Base64 conversion completed, length: 20560
💾 Saving logo data to Firebase...
✅ Logo data saved to Firebase successfully
✅ Preview updated successfully
✅ Logo upload process completed successfully
```

### 6. فحص الصفحة الرئيسية

#### الرسائل المتوقعة في Console:
```
🖼️ Loading site logo...
🔄 Fetching logo data from Firebase...
📊 Logo data received: {url: "data:image/png;base64,iVBORw0KGgo...", enabled: true, alt: "لوجو شركة السلامات"}
✅ Logo found and enabled, displaying...
✅ Logo image loaded successfully
✅ Logo setup completed
```

### 7. الحلول للمشاكل الشائعة

#### المشكلة: اللوجو لا يظهر في الصفحة الرئيسية
**الحل:**
```javascript
// 1. تحقق من وجود العنصر
const logoElement = document.getElementById('company-logo');
console.log('Logo element exists:', !!logoElement);

// 2. فرض إعادة تحميل اللوجو
loadSiteLogo();

// 3. فحص البيانات مباشرة
firebase.database().ref('siteLogo').once('value').then(snapshot => {
    const data = snapshot.val();
    if (data && data.enabled) {
        logoElement.src = data.url;
        logoElement.style.display = 'block';
    }
});
```

#### المشكلة: خطأ في رفع اللوجو
**الحل:**
```javascript
// تحقق من صحة الملف
const fileInput = document.getElementById('logo-file');
const file = fileInput.files[0];
console.log('File details:', {
    name: file.name,
    size: file.size,
    type: file.type,
    sizeInMB: (file.size / 1024 / 1024).toFixed(2)
});
```

#### المشكلة: اللوجو يظهر في المعاينة لكن لا يُحفظ
**الحل:**
```javascript
// تحقق من اتصال Firebase
console.log('Firebase initialized:', !!firebase.apps.length);
console.log('Database reference:', !!adminPanel.database);

// اختبار الكتابة في Firebase
adminPanel.database.ref('test').set({
    timestamp: Date.now(),
    message: 'Test write'
}).then(() => {
    console.log('✅ Firebase write test successful');
}).catch(error => {
    console.error('❌ Firebase write test failed:', error);
});
```

### 8. إعادة تعيين اللوجو
```javascript
// حذف اللوجو من Firebase
firebase.database().ref('siteLogo').remove().then(() => {
    console.log('Logo removed from Firebase');
    // إعادة تحميل الصفحة
    location.reload();
});
```

### 9. اختبار اللوجو بصورة تجريبية
```javascript
// في لوحة الأدمن، إضافة لوجو تجريبي
const testLogoData = {
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiByeD0iOCIgZmlsbD0iIzY2N2VlYSIvPgo8dGV4dCB4PSIzMiIgeT0iMzgiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7YszwvdGV4dD4KPC9zdmc+',
    alt: 'لوجو تجريبي',
    enabled: true,
    fileName: 'test-logo.svg',
    fileSize: 500,
    fileType: 'image/svg+xml',
    uploadedAt: new Date().toISOString()
};

firebase.database().ref('siteLogo').set(testLogoData).then(() => {
    console.log('✅ Test logo saved successfully');
});
```

### 10. فحص الشبكة
في Developer Tools:
1. افتح تبويب Network
2. قم برفع اللوجو
3. تحقق من وجود طلبات Firebase
4. تأكد من عدم وجود أخطاء 403 أو 404

### 11. التحقق من قواعد Firebase
تأكد من أن قواعد Firebase تسمح بالقراءة والكتابة:
```json
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
```

### 12. إعادة تشغيل كاملة
1. أغلق جميع تبويبات المتصفح
2. امسح cache المتصفح
3. افتح الموقع من جديد
4. جرب رفع اللوجو مرة أخرى

## معلومات إضافية
- حجم اللوجو الأقصى: 2MB
- الصيغ المدعومة: PNG, JPG, SVG
- الأبعاد المُفضلة: 64x64 بكسل
- يُفضل PNG بخلفية شفافة

## الدعم
إذا استمرت المشكلة، تحقق من:
1. إعدادات Firebase
2. اتصال الإنترنت
3. صلاحيات المتصفح
4. إعدادات الأمان في المتصفح
