# إصلاح عرض النصوص الإنجليزية في قسم الفروع

## المشكلة المكتشفة
عند تبديل اللغة إلى الإنجليزية في الصفحة الرئيسية، قسم "فروعنا" لا يظهر النصوص الإنجليزية (أسماء الفروع والعناوين والتسميات) بل يبقى يظهر النصوص العربية.

## سبب المشكلة
كود تحميل الفروع في `index.html` كان يستخدم نصوص ثابتة بالعربية ولا يدعم الحقول متعددة اللغات. النصوص كانت مكتوبة مباشرة بدون خصائص `data-ar` و `data-en`.

### المشكلة في الكود:
```javascript
// كود قديم - نصوص ثابتة بالعربية
branchCard.innerHTML = `
    <h3>${branch.name || 'فرع غير محدد'}</h3>
    <p><strong>العنوان:</strong> ${branch.address || 'عنوان غير محدد'}</p>
    ${branch.phone ? `<p><strong>الهاتف:</strong> ${branch.phone}</p>` : ''}
    <a href="..." class="location-btn">📍 الموقع</a>
`;
```

## الإصلاحات المطبقة

### 1. تحديث كود تحميل الفروع الأولي

#### قبل الإصلاح:
```javascript
branchCard.innerHTML = `
    <h3>${branch.name || 'فرع غير محدد'}</h3>
    <p><strong>العنوان:</strong> ${branch.address || 'عنوان غير محدد'}</p>
    ${branch.phone ? `<p><strong>الهاتف:</strong> ${branch.phone}</p>` : ''}
    <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || branch.name)}"
       target="_blank" class="location-btn">📍 الموقع</a>
`;
```

#### بعد الإصلاح:
```javascript
// Get current language for display
const currentLang = document.documentElement.getAttribute('data-lang') || 'ar';

// Get bilingual content
const nameToShow = currentLang === 'en' && branch.nameEn ? branch.nameEn : (branch.nameAr || branch.name || 'فرع غير محدد');
const addressToShow = currentLang === 'en' && branch.addressEn ? branch.addressEn : (branch.addressAr || branch.address || 'عنوان غير محدد');
const addressLabel = currentLang === 'en' ? 'Address:' : 'العنوان:';
const phoneLabel = currentLang === 'en' ? 'Phone:' : 'الهاتف:';
const locationText = currentLang === 'en' ? '📍 Location' : '📍 الموقع';

branchCard.innerHTML = `
    <h3 data-ar="${branch.nameAr || branch.name || 'فرع غير محدد'}" data-en="${branch.nameEn || 'Branch'}">${nameToShow}</h3>
    <p><strong data-ar="العنوان:" data-en="Address:">${addressLabel}</strong> <span data-ar="${branch.addressAr || branch.address || 'عنوان غير محدد'}" data-en="${branch.addressEn || 'Address not specified'}">${addressToShow}</span></p>
    ${branch.phone ? `<p><strong data-ar="الهاتف:" data-en="Phone:">${phoneLabel}</strong> ${branch.phone}</p>` : ''}
    <a href="https://maps.google.com/?q=${encodeURIComponent(addressToShow || branch.name)}"
       target="_blank" class="location-btn" data-ar="📍 الموقع" data-en="📍 Location">${locationText}</a>
`;
```

### 2. تحديث مستمع التحديث المباشر للفروع

تم تطبيق نفس الإصلاح على مستمع `database.ref('branches').on('value')` لضمان أن التحديثات المباشرة تدعم اللغتين أيضاً.

### 3. إضافة وظيفة تحديث لغة الفروع

```javascript
function updateBranchesLanguage() {
    try {
        const branchCards = document.querySelectorAll('.branch-card');
        
        branchCards.forEach(card => {
            // Update branch name
            const nameElement = card.querySelector('h3[data-ar][data-en]');
            if (nameElement) {
                const nameText = currentLanguage === 'en' ? nameElement.getAttribute('data-en') : nameElement.getAttribute('data-ar');
                nameElement.textContent = nameText;
            }
            
            // Update address label
            const addressLabel = card.querySelector('strong[data-ar][data-en]');
            if (addressLabel) {
                const labelText = currentLanguage === 'en' ? addressLabel.getAttribute('data-en') : addressLabel.getAttribute('data-ar');
                addressLabel.textContent = labelText;
            }
            
            // Update address text
            const addressSpan = card.querySelector('span[data-ar][data-en]');
            if (addressSpan) {
                const addressText = currentLanguage === 'en' ? addressSpan.getAttribute('data-en') : addressSpan.getAttribute('data-ar');
                addressSpan.textContent = addressText;
            }
            
            // Update phone label
            const phoneLabels = card.querySelectorAll('strong[data-ar][data-en]');
            phoneLabels.forEach(label => {
                if (label.getAttribute('data-ar') === 'الهاتف:') {
                    const phoneText = currentLanguage === 'en' ? label.getAttribute('data-en') : label.getAttribute('data-ar');
                    label.textContent = phoneText;
                }
            });
            
            // Update location button
            const locationBtn = card.querySelector('.location-btn[data-ar][data-en]');
            if (locationBtn) {
                const locationText = currentLanguage === 'en' ? locationBtn.getAttribute('data-en') : locationBtn.getAttribute('data-ar');
                locationBtn.textContent = locationText;
            }
        });
        
        console.log('Branches language updated to:', currentLanguage);
    } catch (error) {
        console.error('Error updating branches language:', error);
    }
}
```

### 4. ربط تحديث الفروع بنظام تبديل اللغة

```javascript
// في وظيفة updateLanguage
// Update branches language
updateBranchesLanguage();
```

## المميزات الجديدة

### ✅ دعم كامل للغتين في قسم الفروع:
- **أسماء الفروع**: `nameAr` / `nameEn`
- **العناوين**: `addressAr` / `addressEn`
- **تسميات الحقول**: "العنوان/Address"، "الهاتف/Phone"
- **زر الموقع**: "📍 الموقع/📍 Location"

### ✅ التوافق العكسي:
- يدعم البيانات القديمة (`name`, `address`)
- يعمل مع البيانات الجديدة (`nameAr`, `nameEn`, `addressAr`, `addressEn`)
- لا يؤثر على الفروع الموجودة

### ✅ تحديث فوري:
- عند تبديل اللغة، تتحدث جميع النصوص فوراً
- لا حاجة لإعادة تحميل الصفحة
- تجربة مستخدم سلسة

### ✅ تحديث مباشر:
- التحديثات من لوحة الإدارة تظهر فوراً
- دعم اللغتين في التحديثات المباشرة
- تجربة متسقة

## اختبار الإصلاح

### خطوات الاختبار:
1. **فتح الصفحة الرئيسية**
2. **التأكد من وجود فروع في قسم "فروعنا"**
3. **الضغط على زر تبديل اللغة 🌐**
4. **التحقق من تحديث النصوص فوراً**

### النتائج المتوقعة:

#### 🇸🇦 عند اختيار العربية:
- أسماء الفروع تظهر بالعربية
- العناوين تظهر بالعربية
- التسميات: "العنوان:"، "الهاتف:"
- زر الموقع: "📍 الموقع"

#### 🇺🇸 عند اختيار الإنجليزية:
- أسماء الفروع تظهر بالإنجليزية
- العناوين تظهر بالإنجليزية
- التسميات: "Address:"، "Phone:"
- زر الموقع: "📍 Location"

## هيكل البيانات المطلوب

### في Firebase - قاعدة البيانات:
```json
{
  "branches": {
    "branch1": {
      "nameAr": "الفرع الرئيسي",
      "nameEn": "Main Branch",
      "addressAr": "الرياض، المملكة العربية السعودية",
      "addressEn": "Riyadh, Saudi Arabia",
      "phone": "+966123456789",
      
      // للتوافق العكسي
      "name": "الفرع الرئيسي",
      "address": "الرياض، المملكة العربية السعودية"
    }
  }
}
```

### في لوحة الإدارة:
عند إضافة أو تعديل فرع، يجب ملء:
- **الاسم بالعربية** (`nameAr`)
- **الاسم بالإنجليزية** (`nameEn`)
- **العنوان بالعربية** (`addressAr`)
- **العنوان بالإنجليزية** (`addressEn`)
- **رقم الهاتف** (`phone`)

## الملفات المحدثة

### index.html
- **السطور 315-342**: تحديث كود تحميل الفروع الأولي
- **السطور 509-539**: تحديث مستمع التحديث المباشر
- **السطور 709-711**: ربط تحديث الفروع بنظام تبديل اللغة
- **السطور 717-767**: إضافة وظيفة `updateBranchesLanguage`

## التحسينات الإضافية

### 🔄 تحديث تلقائي:
- الفروع تتحدث تلقائياً عند تبديل اللغة
- لا حاجة لتدخل المستخدم
- تجربة سلسة ومتسقة

### 🌐 دعم شامل للغتين:
- جميع عناصر الفروع تدعم اللغتين
- ترجمة كاملة لجميع التسميات
- تصميم متجاوب مع اتجاه النص

### 📱 تجربة مستخدم محسنة:
- تحديث فوري بدون تأخير
- انتقالات سلسة بين اللغات
- حفظ تفضيلات اللغة

## الخلاصة

تم إصلاح مشكلة عرض النصوص الإنجليزية في قسم الفروع بنجاح! الآن:

- **✅ قسم الفروع يدعم اللغتين بالكامل**
- **✅ تحديث فوري عند تبديل اللغة**
- **✅ التوافق العكسي مع البيانات القديمة**
- **✅ تجربة مستخدم متسقة وسلسة**
- **✅ ترجمة شاملة لجميع العناصر**

النظام الآن يوفر تجربة متكاملة ومتسقة للمستخدمين باللغتين العربية والإنجليزية في جميع أقسام الموقع! 🎉

## ملاحظات للمطورين

### نصائح للصيانة:
1. **عند إضافة فروع جديدة**: تأكد من ملء الحقول باللغتين
2. **عند تحديث الفروع**: استخدم `nameAr/nameEn` و `addressAr/addressEn`
3. **اختبار دوري**: تأكد من عمل تبديل اللغة بشكل صحيح
4. **التحديث المباشر**: تأكد من أن التحديثات تظهر فوراً

### أدوات التشخيص:
- **وحدة التحكم**: مراقبة رسائل `Branches language updated to:`
- **Developer Tools**: فحص خصائص `data-ar` و `data-en`
- **Network Tab**: مراقبة تحميل البيانات من Firebase
