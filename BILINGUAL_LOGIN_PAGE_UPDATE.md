# تحديث صفحة التسجيل لدعم اللغتين

## نظرة عامة
تم تحديث صفحة التسجيل `login.html` لتدعم اللغتين العربية والإنجليزية تلقائياً حسب لغة الموقع الرئيسي. الآن عندما يضغط المستخدم على "التسجيل" من الموقع، ستظهر صفحة التسجيل باللغة المناسبة.

## التحديثات المنجزة

### ✅ إضافة نظام اللغات الكامل

#### 1. تحديث HTML الأساسي:
```html
<!-- قبل التحديث -->
<html lang="ar" dir="rtl">
<title>التسجيل - AL-SALAMAT</title>

<!-- بعد التحديث -->
<html lang="ar" dir="rtl" data-lang="ar">
<title data-ar="التسجيل - AL-SALAMAT" data-en="Login - AL-SALAMAT">التسجيل - AL-SALAMAT</title>
```

#### 2. إضافة زر تبديل اللغة:
```html
<div class="language-toggle-container" style="position: absolute; top: 20px; right: 20px;">
    <button class="language-toggle" onclick="toggleLanguage()" id="language-toggle">
        <span class="lang-icon">🌐</span>
        <span class="lang-text" id="lang-text">EN</span>
    </button>
</div>
```

### ✅ تحديث جميع النصوص لدعم اللغتين

#### 1. تبويبات النماذج:
```html
<!-- قبل التحديث -->
<button class="tab-btn active" onclick="switchTab('login')">تسجيل الدخول</button>
<button class="tab-btn" onclick="switchTab('register')">إنشاء حساب</button>

<!-- بعد التحديث -->
<button class="tab-btn active" onclick="switchTab('login')" data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</button>
<button class="tab-btn" onclick="switchTab('register')" data-ar="إنشاء حساب" data-en="Register">إنشاء حساب</button>
```

#### 2. نموذج تسجيل الدخول:
```html
<!-- قبل التحديث -->
<label for="login-email">البريد الإلكتروني</label>
<label for="login-password">كلمة المرور</label>
<button type="submit" class="submit-btn">تسجيل الدخول</button>

<!-- بعد التحديث -->
<label for="login-email" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
<label for="login-password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
<button type="submit" class="submit-btn" data-ar="تسجيل الدخول" data-en="Login">تسجيل الدخول</button>
```

#### 3. نموذج التسجيل:
```html
<!-- قبل التحديث -->
<label for="register-name">الاسم الكامل</label>
<label for="register-email">البريد الإلكتروني</label>
<label for="register-phone">رقم الهاتف</label>
<label for="register-password">كلمة المرور</label>
<label for="register-confirm-password">تأكيد كلمة المرور</label>
<button type="submit" class="submit-btn">إنشاء حساب</button>

<!-- بعد التحديث -->
<label for="register-name" data-ar="الاسم الكامل" data-en="Full Name">الاسم الكامل</label>
<label for="register-email" data-ar="البريد الإلكتروني" data-en="Email">البريد الإلكتروني</label>
<label for="register-phone" data-ar="رقم الهاتف" data-en="Phone Number">رقم الهاتف</label>
<label for="register-password" data-ar="كلمة المرور" data-en="Password">كلمة المرور</label>
<label for="register-confirm-password" data-ar="تأكيد كلمة المرور" data-en="Confirm Password">تأكيد كلمة المرور</label>
<button type="submit" class="submit-btn" data-ar="إنشاء حساب" data-en="Create Account">إنشاء حساب</button>
```

### ✅ إضافة وظائف JavaScript للغات

#### 1. تهيئة اللغة عند تحميل الصفحة:
```javascript
// Language Management
let currentLanguage = localStorage.getItem('language') || 'ar';

// Initialize language on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
});

function initializeLanguage() {
    // Set initial language from localStorage
    currentLanguage = localStorage.getItem('language') || 'ar';
    
    // Update HTML attributes
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('data-lang', currentLanguage);
    
    // Update page title
    const title = document.querySelector('title');
    if (title) {
        title.textContent = currentLanguage === 'ar' ? 'التسجيل - AL-SALAMAT' : 'Login - AL-SALAMAT';
    }
    
    // Update all elements
    updateLanguage();
}
```

#### 2. وظيفة تبديل اللغة:
```javascript
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    localStorage.setItem('language', currentLanguage);
    
    // Update HTML attributes
    document.documentElement.lang = currentLanguage;
    document.documentElement.dir = currentLanguage === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('data-lang', currentLanguage);
    
    // Update page title
    const title = document.querySelector('title');
    if (title) {
        title.textContent = currentLanguage === 'ar' ? 'التسجيل - AL-SALAMAT' : 'Login - AL-SALAMAT';
    }
    
    updateLanguage();
}
```

#### 3. تحديث جميع العناصر:
```javascript
function updateLanguage() {
    const elements = document.querySelectorAll('[data-ar][data-en]');
    const langToggleText = document.getElementById('lang-text');

    elements.forEach(element => {
        if (currentLanguage === 'ar') {
            element.textContent = element.getAttribute('data-ar');
        } else {
            element.textContent = element.getAttribute('data-en');
        }
    });

    // Update language toggle button text
    if (langToggleText) {
        langToggleText.textContent = currentLanguage === 'ar' ? 'EN' : 'ع';
    }

    // Update body class for styling
    document.body.className = document.body.className.replace(/lang-\w+/, '');
    document.body.classList.add(`lang-${currentLanguage}`);
}
```

### ✅ تحديث رسائل الخطأ والنجاح

#### 1. رسائل الخطأ متعددة اللغات:
```javascript
function getErrorMessage(errorCode) {
    const messages = {
        'auth/email-already-in-use': {
            ar: 'هذا البريد الإلكتروني مستخدم بالفعل',
            en: 'This email is already in use'
        },
        'auth/weak-password': {
            ar: 'كلمة المرور ضعيفة جداً',
            en: 'Password is too weak'
        },
        'auth/invalid-email': {
            ar: 'البريد الإلكتروني غير صحيح',
            en: 'Invalid email address'
        },
        // ... المزيد من الرسائل
    };

    const message = messages[errorCode];
    if (message) {
        return currentLanguage === 'ar' ? message.ar : message.en;
    }
    
    return currentLanguage === 'ar' ? 
        'حدث خطأ غير متوقع، حاول مرة أخرى' : 
        'An unexpected error occurred, please try again';
}
```

#### 2. رسائل النجاح:
```javascript
// في وظيفة تسجيل الدخول
const successMessage = currentLanguage === 'ar' ? 
    'تم تسجيل الدخول بنجاح!' : 
    'Login successful!';
showMessage(successMessage, 'success');

// في وظيفة التسجيل
const successMessage = currentLanguage === 'ar' ? 
    'تم إنشاء الحساب بنجاح!' : 
    'Account created successfully!';
showMessage(successMessage, 'success');
```

#### 3. رسائل التحقق:
```javascript
// التحقق من تطابق كلمة المرور
if (password !== confirmPassword) {
    const errorMessage = currentLanguage === 'ar' ? 
        'كلمة المرور وتأكيد كلمة المرور غير متطابقتين' : 
        'Password and confirm password do not match';
    showMessage(errorMessage, 'error');
    return;
}
```

## السلوك الجديد للنظام

### 1. عند الدخول من الموقع الرئيسي:
- **إذا كان الموقع بالعربية**: صفحة التسجيل تظهر بالعربية
- **إذا كان الموقع بالإنجليزية**: صفحة التسجيل تظهر بالإنجليزية

### 2. تبديل اللغة في صفحة التسجيل:
- **زر 🌐**: يبدل بين العربية والإنجليزية
- **حفظ التفضيل**: يحفظ اللغة في localStorage
- **تحديث فوري**: جميع النصوص تتغير فوراً

### 3. اتجاه النص:
- **العربية**: RTL (من اليمين لليسار)
- **الإنجليزية**: LTR (من اليسار لليمين)

### 4. العودة للموقع الرئيسي:
- **حفظ اللغة**: اللغة المختارة تنتقل للموقع الرئيسي
- **تجربة متسقة**: نفس اللغة في جميع الصفحات

## المزايا الجديدة

### ✅ تجربة مستخدم محسنة:
- **تلقائية**: لا حاجة لتغيير اللغة يدوياً
- **متسقة**: نفس اللغة في جميع الصفحات
- **سهولة**: تبديل سريع بين اللغات

### ✅ إمكانية الوصول:
- **دعم RTL/LTR**: اتجاه صحيح للنص
- **رسائل واضحة**: خطأ ونجاح بلغة المستخدم
- **تصميم متجاوب**: يعمل على جميع الأجهزة

### ✅ سهولة الصيانة:
- **نظام موحد**: نفس نظام اللغات في الموقع
- **قابلية التوسع**: سهولة إضافة لغات جديدة
- **كود منظم**: وظائف منفصلة لكل ميزة

## اختبار النظام

### 1. اختبار التحويل التلقائي:
```javascript
// في الموقع الرئيسي
localStorage.setItem('language', 'en');
// ثم الضغط على رابط التسجيل
// النتيجة المتوقعة: صفحة التسجيل بالإنجليزية
```

### 2. اختبار تبديل اللغة:
- الضغط على زر 🌐 في صفحة التسجيل
- النتيجة المتوقعة: تغيير جميع النصوص فوراً

### 3. اختبار الرسائل:
- إدخال بيانات خاطئة
- النتيجة المتوقعة: رسائل خطأ باللغة المختارة

## الملفات المحدثة

### login.html
- **السطر 2**: إضافة `data-lang="ar"`
- **السطر 6**: إضافة دعم اللغتين للعنوان
- **السطور 207-213**: إضافة زر تبديل اللغة
- **السطور 216-219**: تحديث تبويبات النماذج
- **السطور 225-241**: تحديث نموذج تسجيل الدخول
- **السطور 243-268**: تحديث نموذج التسجيل
- **السطر 271**: تحديث رابط العودة
- **السطور 287-360**: إضافة وظائف إدارة اللغات
- **السطور 398-438**: تحديث رسائل الخطأ
- **السطور 452-455**: تحديث رسائل النجاح

## التوافق العكسي

### ✅ المستخدمون الحاليون:
- جميع الحسابات الموجودة تعمل بشكل طبيعي
- لا تأثير على عملية تسجيل الدخول

### ✅ الروابط الموجودة:
- رابط `login.html` يعمل بشكل طبيعي
- اللغة الافتراضية هي العربية

## الخلاصة

تم تحديث صفحة التسجيل بنجاح لتدعم اللغتين العربية والإنجليزية! الآن:

- **🌐 تحويل تلقائي**: صفحة التسجيل تظهر بلغة الموقع
- **🔄 تبديل سهل**: زر تبديل اللغة في الصفحة
- **📱 تجربة متسقة**: نفس اللغة في جميع الصفحات
- **✅ رسائل واضحة**: خطأ ونجاح بلغة المستخدم

النظام جاهز للاستخدام ويوفر تجربة ممتازة للمستخدمين باللغتين! 🎉
