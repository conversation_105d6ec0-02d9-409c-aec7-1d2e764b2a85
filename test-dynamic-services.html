<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة صور الخدمات الديناميكي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .service-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .service-item {
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .service-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #e9ecef;
        }
        .service-content {
            padding: 15px;
        }
        .service-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .service-description {
            color: #666;
            line-height: 1.5;
        }
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .test-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn.primary { background: #007bff; color: white; }
        .test-btn.success { background: #28a745; color: white; }
        .test-btn.warning { background: #ffc107; color: black; }
        .test-btn.danger { background: #dc3545; color: white; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .language-toggle {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار نظام إدارة صور الخدمات الديناميكي</h1>
        
        <button class="language-toggle" onclick="toggleLanguage()">
            🌐 تبديل اللغة (Current: <span id="current-lang">العربية</span>)
        </button>

        <!-- Service Images Preview -->
        <div class="test-section">
            <h2>🔧 معاينة صور الخدمات</h2>
            <div class="service-preview" id="services-preview">
                <div class="no-data">
                    <p>جاري تحميل صور الخدمات...</p>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h2>🔧 أدوات الاختبار</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="loadServicesFromFirebase()">تحميل من Firebase</button>
                <button class="test-btn success" onclick="testEmptyState()">اختبار الحالة الفارغة</button>
                <button class="test-btn warning" onclick="testPartialData()">اختبار البيانات الجزئية</button>
                <button class="test-btn danger" onclick="clearServices()">مسح الخدمات</button>
            </div>
            
            <div id="test-results"></div>
        </div>

        <!-- Mock Data Testing -->
        <div class="test-section">
            <h2>📊 اختبار البيانات التجريبية</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="loadMockData('full')">بيانات كاملة</button>
                <button class="test-btn warning" onclick="loadMockData('partial')">بيانات جزئية</button>
                <button class="test-btn danger" onclick="loadMockData('empty')">بيانات فارغة</button>
            </div>
        </div>

        <!-- Instructions -->
        <div class="test-section">
            <h2>📋 تعليمات الاختبار</h2>
            <ol>
                <li>اضغط على "تحميل من Firebase" لتحميل البيانات الفعلية</li>
                <li>اختبر تبديل اللغة للتأكد من عرض المحتوى الصحيح</li>
                <li>اختبر الحالات المختلفة (بيانات كاملة/جزئية/فارغة)</li>
                <li>تأكد من عرض الرسائل المناسبة في كل حالة</li>
                <li>تحقق من عمل التحديث الفوري</li>
            </ol>
        </div>
    </div>

    <script>
        let currentLanguage = 'ar';
        let mockFirebaseData = null;

        // Mock Firebase data for testing
        const mockData = {
            full: {
                car2: {
                    titleAr: "خدمات زجاج السيارات",
                    titleEn: "Car Glass Services",
                    descriptionAr: "أفضل خدمات تركيب وإصلاح زجاج السيارات",
                    descriptionEn: "Best car glass installation and repair services",
                    url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDA5NmZmIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7Yrtiv2YXYp9iqINiy2KzYp9isINin2YTYs9mK2KfYsdin2Ko8L3RleHQ+PC9zdmc+",
                    fileName: "car-glass-services.jpg"
                },
                car4: {
                    titleAr: "تركيب زجاج السيارات",
                    titleEn: "Car Glass Installation",
                    descriptionAr: "تركيب احترافي بأعلى معايير الجودة",
                    descriptionEn: "Professional installation with highest quality standards",
                    url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMjhhNzQ1Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7Yqtix2YPZitioINiy2KzYp9isINin2YTYs9mK2KfYsdin2Ko8L3RleHQ+PC9zdmc+",
                    fileName: "car-glass-installation.jpg"
                },
                car6: {
                    titleAr: "إصلاح زجاج السيارات",
                    titleEn: "Car Glass Repair",
                    descriptionAr: "إصلاح سريع وفعال لجميع أنواع السيارات",
                    descriptionEn: "Fast and effective repair for all car types",
                    url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZjMTA3Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0iYmxhY2siIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7Ypdiz2YTYp9itINiy2KzYp9isINin2YTYs9mK2KfYsdin2Ko8L3RleHQ+PC9zdmc+",
                    fileName: "car-glass-repair.jpg"
                }
            },
            partial: {
                car2: {
                    titleAr: "خدمات زجاج السيارات",
                    url: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNDA5NmZmIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyMCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7YqNmK2KfZhtin2Kog2KzYstim2YrYqTwvdGV4dD48L3N2Zz4=",
                    fileName: "partial-data.jpg"
                },
                car4: {
                    titleAr: "تركيب زجاج السيارات",
                    titleEn: "Car Glass Installation"
                    // No URL - testing missing image
                }
            },
            empty: {}
        };

        function toggleLanguage() {
            currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
            document.getElementById('current-lang').textContent = currentLanguage === 'ar' ? 'العربية' : 'English';
            updateLanguage();
        }

        function updateLanguage() {
            const elements = document.querySelectorAll('[data-ar][data-en]');
            elements.forEach(element => {
                if (currentLanguage === 'ar') {
                    element.textContent = element.getAttribute('data-ar');
                } else {
                    element.textContent = element.getAttribute('data-en');
                }
            });
            
            // Update service content based on current language
            renderServices(mockFirebaseData);
        }

        function renderServices(data) {
            const container = document.getElementById('services-preview');
            
            if (!data || Object.keys(data).length === 0) {
                container.innerHTML = `
                    <div class="no-data">
                        <p data-ar="لا توجد صور خدمات حالياً" data-en="No service images available">لا توجد صور خدمات حالياً</p>
                    </div>
                `;
                updateLanguage();
                return;
            }

            container.innerHTML = '';
            const serviceOrder = ['car2', 'car4', 'car6'];
            
            serviceOrder.forEach(serviceType => {
                const serviceData = data[serviceType];
                if (serviceData) {
                    const serviceItem = document.createElement('div');
                    serviceItem.className = 'service-item';
                    
                    const titleToShow = currentLanguage === 'en' && serviceData.titleEn ? serviceData.titleEn : (serviceData.titleAr || 'لا يوجد عنوان');
                    const descToShow = currentLanguage === 'en' && serviceData.descriptionEn ? serviceData.descriptionEn : (serviceData.descriptionAr || 'لا يوجد وصف');
                    
                    serviceItem.innerHTML = `
                        ${serviceData.url ? 
                            `<img src="${serviceData.url}" alt="${titleToShow}" class="service-image">` :
                            `<div class="service-image" style="display: flex; align-items: center; justify-content: center; background: #e9ecef; color: #666;">لا توجد صورة</div>`
                        }
                        <div class="service-content">
                            <div class="service-title" data-ar="${serviceData.titleAr || 'لا يوجد عنوان'}" data-en="${serviceData.titleEn || 'No title'}">${titleToShow}</div>
                            <div class="service-description" data-ar="${serviceData.descriptionAr || 'لا يوجد وصف'}" data-en="${serviceData.descriptionEn || 'No description'}">${descToShow}</div>
                            <small style="color: #999; margin-top: 10px; display: block;">
                                النوع: ${serviceType} | الملف: ${serviceData.fileName || 'غير محدد'}
                            </small>
                        </div>
                    `;
                    container.appendChild(serviceItem);
                }
            });
        }

        function loadMockData(type) {
            mockFirebaseData = mockData[type];
            renderServices(mockFirebaseData);
            
            const results = document.getElementById('test-results');
            results.innerHTML = `<div class="status info"><h4>📊 تم تحميل البيانات التجريبية: ${type}</h4><p>عدد الخدمات: ${Object.keys(mockFirebaseData).length}</p></div>`;
        }

        function loadServicesFromFirebase() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="status warning"><h4>🔄 محاولة الاتصال بـ Firebase...</h4><p>هذا اختبار تجريبي - في التطبيق الفعلي سيتم تحميل البيانات من Firebase</p></div>';
            
            // Simulate Firebase loading
            setTimeout(() => {
                results.innerHTML = '<div class="status error"><h4>❌ لا يمكن الاتصال بـ Firebase في البيئة التجريبية</h4><p>استخدم البيانات التجريبية للاختبار</p></div>';
            }, 2000);
        }

        function testEmptyState() {
            loadMockData('empty');
        }

        function testPartialData() {
            loadMockData('partial');
        }

        function clearServices() {
            mockFirebaseData = null;
            renderServices(null);
            
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="status success"><h4>✅ تم مسح جميع الخدمات</h4><p>يتم عرض الحالة الفارغة الآن</p></div>';
        }

        // Initialize with full data
        loadMockData('full');
    </script>
</body>
</html>
