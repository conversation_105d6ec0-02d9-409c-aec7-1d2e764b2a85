<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة التسجيل متعددة اللغات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .test-btn.primary { background: #007bff; color: white; }
        .test-btn.success { background: #28a745; color: white; }
        .test-btn.warning { background: #ffc107; color: black; }
        .test-btn.info { background: #17a2b8; color: white; }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .language-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .demo-card h4 {
            margin-top: 0;
            color: #495057;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
        }
        .current-lang {
            background: #007bff;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin-left: 10px;
        }
        .iframe-container {
            border: 2px solid #007bff;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        .iframe-header {
            background: #007bff;
            color: white;
            padding: 10px;
            font-weight: bold;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار صفحة التسجيل متعددة اللغات</h1>
        
        <!-- Current Language Status -->
        <div class="test-section">
            <h2>🌐 حالة اللغة الحالية</h2>
            <div class="status info">
                <strong>اللغة المحفوظة في localStorage:</strong> 
                <span id="current-stored-lang">غير محدد</span>
                <span class="current-lang" id="lang-indicator">AR</span>
            </div>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="setLanguage('ar')">تعيين العربية</button>
                <button class="test-btn primary" onclick="setLanguage('en')">Set English</button>
                <button class="test-btn warning" onclick="clearLanguage()">مسح اللغة</button>
                <button class="test-btn info" onclick="checkLanguage()">فحص اللغة</button>
            </div>
        </div>

        <!-- Test Scenarios -->
        <div class="test-section">
            <h2>🔧 سيناريوهات الاختبار</h2>
            <div class="language-demo">
                <div class="demo-card">
                    <h4>🇸🇦 الاختبار بالعربية</h4>
                    <ol>
                        <li>اضغط على "تعيين العربية"</li>
                        <li>اضغط على "فتح صفحة التسجيل"</li>
                        <li>تحقق من ظهور الصفحة بالعربية</li>
                        <li>جرب تبديل اللغة داخل الصفحة</li>
                    </ol>
                </div>
                <div class="demo-card">
                    <h4>🇺🇸 English Testing</h4>
                    <ol>
                        <li>Click "Set English"</li>
                        <li>Click "Open Login Page"</li>
                        <li>Verify page appears in English</li>
                        <li>Try language toggle inside page</li>
                    </ol>
                </div>
            </div>
            <div class="test-buttons">
                <a href="login.html" target="_blank" class="test-btn success">فتح صفحة التسجيل</a>
                <a href="index.html" target="_blank" class="test-btn info">فتح الموقع الرئيسي</a>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="test-section">
            <h2>🌟 المميزات المطبقة</h2>
            <div class="language-demo">
                <div class="demo-card">
                    <h4>🔄 التحويل التلقائي</h4>
                    <ul class="feature-list">
                        <li>قراءة اللغة من localStorage</li>
                        <li>تطبيق اللغة عند تحميل الصفحة</li>
                        <li>تحديث اتجاه النص (RTL/LTR)</li>
                        <li>تحديث عنوان الصفحة</li>
                    </ul>
                </div>
                <div class="demo-card">
                    <h4>🌐 تبديل اللغة</h4>
                    <ul class="feature-list">
                        <li>زر تبديل اللغة في الصفحة</li>
                        <li>تحديث فوري لجميع النصوص</li>
                        <li>حفظ التفضيل في localStorage</li>
                        <li>تحديث اتجاه النص فوراً</li>
                    </ul>
                </div>
                <div class="demo-card">
                    <h4>📝 النماذج متعددة اللغات</h4>
                    <ul class="feature-list">
                        <li>تبويبات تسجيل الدخول/التسجيل</li>
                        <li>تسميات الحقول</li>
                        <li>أزرار الإرسال</li>
                        <li>روابط المساعدة</li>
                    </ul>
                </div>
                <div class="demo-card">
                    <h4>💬 الرسائل متعددة اللغات</h4>
                    <ul class="feature-list">
                        <li>رسائل الخطأ</li>
                        <li>رسائل النجاح</li>
                        <li>رسائل التحقق</li>
                        <li>رسائل التأكيد</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h2>📊 نتائج الاختبار</h2>
            <div id="test-results">
                <div class="status info">
                    <strong>حالة الاختبار:</strong> جاهز للبدء
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="test-section">
            <h2>📋 تعليمات الاختبار التفصيلية</h2>
            <div class="language-demo">
                <div class="demo-card">
                    <h4>🧪 اختبار التحويل التلقائي</h4>
                    <ol>
                        <li><strong>تعيين اللغة العربية:</strong>
                            <ul>
                                <li>اضغط "تعيين العربية"</li>
                                <li>افتح صفحة التسجيل</li>
                                <li>تحقق من ظهور النصوص بالعربية</li>
                                <li>تحقق من اتجاه النص RTL</li>
                            </ul>
                        </li>
                        <li><strong>تعيين اللغة الإنجليزية:</strong>
                            <ul>
                                <li>اضغط "Set English"</li>
                                <li>افتح صفحة التسجيل</li>
                                <li>تحقق من ظهور النصوص بالإنجليزية</li>
                                <li>تحقق من اتجاه النص LTR</li>
                            </ul>
                        </li>
                    </ol>
                </div>
                <div class="demo-card">
                    <h4>🔄 اختبار تبديل اللغة</h4>
                    <ol>
                        <li><strong>داخل صفحة التسجيل:</strong>
                            <ul>
                                <li>اضغط على زر 🌐</li>
                                <li>تحقق من تغيير جميع النصوص</li>
                                <li>تحقق من تغيير اتجاه النص</li>
                                <li>تحقق من تغيير عنوان الصفحة</li>
                            </ul>
                        </li>
                        <li><strong>العودة للموقع الرئيسي:</strong>
                            <ul>
                                <li>اضغط "العودة للصفحة الرئيسية"</li>
                                <li>تحقق من حفظ اللغة المختارة</li>
                                <li>تحقق من تطبيق اللغة في الموقع</li>
                            </ul>
                        </li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Expected Results -->
        <div class="test-section">
            <h2>✅ النتائج المتوقعة</h2>
            <div class="language-demo">
                <div class="demo-card">
                    <h4>🇸🇦 عند اختيار العربية</h4>
                    <ul class="feature-list">
                        <li>عنوان الصفحة: "التسجيل - AL-SALAMAT"</li>
                        <li>اتجاه النص: من اليمين لليسار (RTL)</li>
                        <li>تبويب: "تسجيل الدخول" و "إنشاء حساب"</li>
                        <li>حقول: "البريد الإلكتروني"، "كلمة المرور"</li>
                        <li>زر: "تسجيل الدخول"</li>
                        <li>رابط: "← العودة للصفحة الرئيسية"</li>
                    </ul>
                </div>
                <div class="demo-card">
                    <h4>🇺🇸 When English Selected</h4>
                    <ul class="feature-list">
                        <li>Page title: "Login - AL-SALAMAT"</li>
                        <li>Text direction: Left to Right (LTR)</li>
                        <li>Tabs: "Login" and "Register"</li>
                        <li>Fields: "Email", "Password"</li>
                        <li>Button: "Login"</li>
                        <li>Link: "← Back to Home"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check current language on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkLanguage();
        });

        function setLanguage(lang) {
            localStorage.setItem('language', lang);
            updateTestResults(`تم تعيين اللغة إلى: ${lang === 'ar' ? 'العربية' : 'English'}`, 'success');
            checkLanguage();
        }

        function clearLanguage() {
            localStorage.removeItem('language');
            updateTestResults('تم مسح إعدادات اللغة', 'warning');
            checkLanguage();
        }

        function checkLanguage() {
            const currentLang = localStorage.getItem('language') || 'غير محدد';
            const langElement = document.getElementById('current-stored-lang');
            const indicatorElement = document.getElementById('lang-indicator');
            
            if (langElement) {
                langElement.textContent = currentLang === 'ar' ? 'العربية' : 
                                        currentLang === 'en' ? 'English' : 'غير محدد';
            }
            
            if (indicatorElement) {
                indicatorElement.textContent = currentLang === 'ar' ? 'AR' : 
                                             currentLang === 'en' ? 'EN' : '??';
                indicatorElement.style.background = currentLang === 'ar' ? '#28a745' : 
                                                   currentLang === 'en' ? '#007bff' : '#6c757d';
            }
            
            updateTestResults(`اللغة الحالية: ${currentLang === 'ar' ? 'العربية' : 
                                                currentLang === 'en' ? 'English' : 'غير محدد'}`, 'info');
        }

        function updateTestResults(message, type) {
            const resultsContainer = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            
            resultsContainer.innerHTML = `
                <div class="status ${type}">
                    <strong>[${timestamp}]</strong> ${message}
                </div>
            `;
        }

        // Test language switching simulation
        function simulateLanguageTest() {
            updateTestResults('بدء اختبار تبديل اللغة...', 'info');
            
            setTimeout(() => {
                setLanguage('ar');
                setTimeout(() => {
                    setLanguage('en');
                    setTimeout(() => {
                        updateTestResults('اكتمل اختبار تبديل اللغة بنجاح!', 'success');
                    }, 1000);
                }, 1000);
            }, 1000);
        }

        // Add test button for automatic testing
        document.addEventListener('DOMContentLoaded', function() {
            const testSection = document.querySelector('.test-section:nth-child(3)');
            if (testSection) {
                const autoTestBtn = document.createElement('button');
                autoTestBtn.className = 'test-btn warning';
                autoTestBtn.textContent = '🤖 اختبار تلقائي';
                autoTestBtn.onclick = simulateLanguageTest;
                
                const buttonsContainer = testSection.querySelector('.test-buttons');
                if (buttonsContainer) {
                    buttonsContainer.appendChild(autoTestBtn);
                }
            }
        });
    </script>
</body>
</html>
